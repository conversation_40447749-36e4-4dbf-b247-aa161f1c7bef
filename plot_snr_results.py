import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import glob

# Configuration
INPUT_FOLDER = 'snr_results_tables'
OUTPUT_FOLDER = 'snr_plots'

# Plot styling configuration
PLOT_STYLES = {
    'CLDNN': {'marker': 'D', 'linestyle': ':', 'color': '#d62728'},
    'AWN': {'marker': 's', 'linestyle': '--', 'color': '#ff7f0e'},
    'MAMC': {'marker': '<', 'linestyle': '--', 'color': '#8c564b'},
    'MAWDN': {'marker': '>', 'linestyle': '-.', 'color': '#e377c2'},
    'WNN-MRNN': {'marker': 'o', 'linestyle': '-', 'color': '#1f77b4'}
}

def create_output_folder():
    """Create output folder for plots"""
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
        print(f"Created output folder: {OUTPUT_FOLDER}")
    else:
        print(f"Output folder already exists: {OUTPUT_FOLDER}")

def get_available_files():
    """Get list of available Excel files"""
    pattern = os.path.join(INPUT_FOLDER, "snr_results_*.xlsx")
    files = glob.glob(pattern)
    return [os.path.basename(f) for f in files]

def extract_dataset_name(filename):
    """Extract dataset name from filename"""
    # Remove 'snr_results_' prefix and '.xlsx' suffix
    return filename.replace('snr_results_', '').replace('.xlsx', '')

def parse_snr_value(snr_str):
    """Parse SNR string to numeric value"""
    try:
        # Remove 'dB' suffix and convert to float
        return float(snr_str.replace('dB', ''))
    except:
        return None

def plot_single_dataset(excel_file, selected_models=None):
    """Plot SNR vs Accuracy for a single dataset"""
    
    # Read Excel file
    file_path = os.path.join(INPUT_FOLDER, excel_file)
    df = pd.read_excel(file_path)
    
    # Extract dataset name
    dataset_name = extract_dataset_name(excel_file)
    
    # Get SNR columns (all columns except 'Model Name')
    snr_columns = [col for col in df.columns if col != 'Model Name']
    
    # Parse SNR values
    snr_values = []
    for col in snr_columns:
        snr_val = parse_snr_value(col)
        if snr_val is not None:
            snr_values.append(snr_val)
    
    if not snr_values:
        print(f"Warning: No valid SNR values found in {excel_file}")
        return
    
    # Sort SNR values
    snr_indices = np.argsort(snr_values)
    sorted_snr_values = [snr_values[i] for i in snr_indices]
    sorted_snr_columns = [snr_columns[i] for i in snr_indices]
    
    # Create plot
    plt.figure(figsize=(12, 8))
    
    # Plot each model
    for _, row in df.iterrows():
        model_name = row['Model Name']
        
        # Skip if model not in selected models (when specified)
        if selected_models and model_name not in selected_models:
            continue
        
        # Extract accuracy values
        accuracies = []
        valid_snr_values = []
        
        for snr_col, snr_val in zip(sorted_snr_columns, sorted_snr_values):
            acc_val = row[snr_col]
            if pd.notna(acc_val) and acc_val != '':
                try:
                    # Convert percentage to decimal (0-1 range)
                    acc_decimal = float(acc_val) / 100.0
                    accuracies.append(acc_decimal)
                    valid_snr_values.append(snr_val)
                except:
                    continue
        
        if not accuracies:
            print(f"Warning: No valid accuracy data for model {model_name}")
            continue
        
        # Get plot style
        style = PLOT_STYLES.get(model_name, {'marker': 'o', 'linestyle': '-', 'color': 'black'})
        
        # Plot line
        plt.plot(valid_snr_values, accuracies, 
                marker=style['marker'], 
                linestyle=style['linestyle'], 
                color=style['color'],
                label=model_name,
                linewidth=2,
                markersize=6)
    
    # Customize plot
    plt.xlabel('SNR (dB)', fontsize=14)
    plt.ylabel('Accuracy', fontsize=14)
    plt.title(f'SNR vs Accuracy - {dataset_name.upper()}', fontsize=16, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    # Set y-axis limits and format
    plt.ylim(0, 1.05)
    plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.0%}'.format(y)))
    
    # Tight layout
    plt.tight_layout()
    
    # Save plot
    output_filename = f"snr_accuracy_{dataset_name}.png"
    output_path = os.path.join(OUTPUT_FOLDER, output_filename)
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Saved plot: {output_path}")

    # Close the plot to free memory
    plt.close()

def interactive_file_selection():
    """Interactive file selection interface"""
    available_files = get_available_files()
    
    if not available_files:
        print(f"No Excel files found in {INPUT_FOLDER}")
        return []
    
    print("\nAvailable dataset files:")
    for i, file in enumerate(available_files, 1):
        dataset_name = extract_dataset_name(file)
        print(f"{i}. {dataset_name}")
    
    print(f"{len(available_files) + 1}. All datasets")
    
    while True:
        try:
            choice = input(f"\nSelect files to plot (1-{len(available_files) + 1}, or comma-separated numbers): ").strip()
            
            if choice == str(len(available_files) + 1):
                return available_files
            
            # Parse comma-separated choices
            choices = [int(x.strip()) for x in choice.split(',')]
            selected_files = []
            
            for c in choices:
                if 1 <= c <= len(available_files):
                    selected_files.append(available_files[c-1])
                else:
                    print(f"Invalid choice: {c}")
                    continue
            
            if selected_files:
                return selected_files
            else:
                print("No valid files selected.")
                
        except ValueError:
            print("Invalid input. Please enter numbers separated by commas.")

def interactive_model_selection(excel_file):
    """Interactive model selection for a specific dataset"""
    file_path = os.path.join(INPUT_FOLDER, excel_file)
    df = pd.read_excel(file_path)
    
    available_models = df['Model Name'].tolist()
    
    print(f"\nAvailable models in {extract_dataset_name(excel_file)}:")
    for i, model in enumerate(available_models, 1):
        print(f"{i}. {model}")
    
    print(f"{len(available_models) + 1}. All models")
    
    while True:
        try:
            choice = input(f"\nSelect models to plot (1-{len(available_models) + 1}, or comma-separated numbers): ").strip()
            
            if choice == str(len(available_models) + 1):
                return None  # All models
            
            # Parse comma-separated choices
            choices = [int(x.strip()) for x in choice.split(',')]
            selected_models = []
            
            for c in choices:
                if 1 <= c <= len(available_models):
                    selected_models.append(available_models[c-1])
                else:
                    print(f"Invalid choice: {c}")
                    continue
            
            if selected_models:
                return selected_models
            else:
                print("No valid models selected.")
                
        except ValueError:
            print("Invalid input. Please enter numbers separated by commas.")

def main():
    """Main function"""
    print("=" * 60)
    print("SNR Results Plotting Tool")
    print("=" * 60)
    
    # Create output folder
    create_output_folder()
    
    # Select files to plot
    selected_files = interactive_file_selection()
    
    if not selected_files:
        print("No files selected. Exiting.")
        return
    
    # Plot each selected file
    for excel_file in selected_files:
        dataset_name = extract_dataset_name(excel_file)
        print(f"\nProcessing dataset: {dataset_name}")
        
        # Ask for model selection
        use_model_selection = input(f"Do you want to select specific models for {dataset_name}? (y/n): ").strip().lower()
        
        if use_model_selection == 'y':
            selected_models = interactive_model_selection(excel_file)
        else:
            selected_models = None
        
        # Create plot
        plot_single_dataset(excel_file, selected_models)
    
    print(f"\n=" * 60)
    print(f"All plots saved to: {OUTPUT_FOLDER}")
    print("=" * 60)

if __name__ == '__main__':
    main()
